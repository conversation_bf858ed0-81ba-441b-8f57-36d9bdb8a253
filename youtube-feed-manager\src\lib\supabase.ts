import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Client-side Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Client component client (for use in client components)
export const createClientSupabase = () => createClient(supabaseUrl, supabaseAnonKey)

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string | null
          full_name: string | null
          avatar_url: string | null
          preferences: any
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          preferences?: any
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string | null
          full_name?: string | null
          avatar_url?: string | null
          preferences?: any
          created_at?: string
          updated_at?: string
        }
      }
      youtube_channels: {
        Row: {
          id: string
          channel_id: string
          channel_name: string
          channel_url: string
          rss_url: string
          description: string | null
          thumbnail_url: string | null
          subscriber_count: number | null
          video_count: number | null
          last_fetched_at: string | null
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          channel_id: string
          channel_name: string
          channel_url: string
          rss_url: string
          description?: string | null
          thumbnail_url?: string | null
          subscriber_count?: number | null
          video_count?: number | null
          last_fetched_at?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          channel_id?: string
          channel_name?: string
          channel_url?: string
          rss_url?: string
          description?: string | null
          thumbnail_url?: string | null
          subscriber_count?: number | null
          video_count?: number | null
          last_fetched_at?: string | null
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      user_subscriptions: {
        Row: {
          id: string
          user_id: string
          channel_id: string
          subscription_name: string | null
          tags: string[]
          priority: number
          is_active: boolean
          notification_enabled: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          channel_id: string
          subscription_name?: string | null
          tags?: string[]
          priority?: number
          is_active?: boolean
          notification_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          channel_id?: string
          subscription_name?: string | null
          tags?: string[]
          priority?: number
          is_active?: boolean
          notification_enabled?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      videos: {
        Row: {
          id: string
          video_id: string
          channel_id: string
          title: string
          description: string | null
          thumbnail_url: string | null
          duration: string | null
          published_at: string
          view_count: number | null
          like_count: number | null
          comment_count: number | null
          video_url: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          video_id: string
          channel_id: string
          title: string
          description?: string | null
          thumbnail_url?: string | null
          duration?: string | null
          published_at: string
          view_count?: number | null
          like_count?: number | null
          comment_count?: number | null
          video_url: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          video_id?: string
          channel_id?: string
          title?: string
          description?: string | null
          thumbnail_url?: string | null
          duration?: string | null
          published_at?: string
          view_count?: number | null
          like_count?: number | null
          comment_count?: number | null
          video_url?: string
          created_at?: string
          updated_at?: string
        }
      }
      user_video_interactions: {
        Row: {
          id: string
          user_id: string
          video_id: string
          status: 'unread' | 'read' | 'saved' | 'hidden' | 'archived'
          rating: number | null
          notes: string | null
          watched_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          video_id: string
          status?: 'unread' | 'read' | 'saved' | 'hidden' | 'archived'
          rating?: number | null
          notes?: string | null
          watched_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          video_id?: string
          status?: 'unread' | 'read' | 'saved' | 'hidden' | 'archived'
          rating?: number | null
          notes?: string | null
          watched_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      content_filters: {
        Row: {
          id: string
          user_id: string
          name: string
          description: string | null
          filter_type: 'include' | 'exclude'
          conditions: any
          is_active: boolean
          priority: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          name: string
          description?: string | null
          filter_type: 'include' | 'exclude'
          conditions: any
          is_active?: boolean
          priority?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          name?: string
          description?: string | null
          filter_type?: 'include' | 'exclude'
          conditions?: any
          is_active?: boolean
          priority?: number
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
}
