
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { ContentSource } from '@/types/content';
import { Settings } from 'lucide-react';

interface SourceConfigDialogProps {
  source: ContentSource;
  onConfigUpdated?: () => void;
  children?: React.ReactNode;
}

export const SourceConfigDialog: React.FC<SourceConfigDialogProps> = ({ 
  source,
  onConfigUpdated,
  children 
}) => {
  const [open, setOpen] = useState(false);
  const [isActive, setIsActive] = useState(source.isActive);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSaveConfiguration = async () => {
    setIsLoading(true);

    try {
      const { error } = await supabase
        .from('youtube_channels')
        .update({ 
          is_active: isActive,
          updated_at: new Date().toISOString()
        })
        .eq('id', source.id);

      if (error) throw error;

      toast({
        title: "Configuration updated",
        description: "Channel settings have been saved successfully.",
      });

      setOpen(false);
      onConfigUpdated?.();
    } catch (error) {
      console.error('Error updating configuration:', error);
      toast({
        title: "Error",
        description: "Failed to update configuration. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="outline" size="sm">
            <Settings className="w-4 h-4 mr-2" />
            Configure
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Configure {source.name}</DialogTitle>
        </DialogHeader>
        <div className="space-y-6 py-4">
          <div className="space-y-2">
            <Label className="text-sm font-medium">Channel Status</Label>
            <div className="flex items-center space-x-2">
              <Switch
                id="active-toggle"
                checked={isActive}
                onCheckedChange={setIsActive}
              />
              <Label htmlFor="active-toggle" className="text-sm">
                {isActive ? 'Active - Channel is being monitored' : 'Inactive - Channel monitoring is paused'}
              </Label>
            </div>
          </div>

          <div className="space-y-2">
            <Label className="text-sm font-medium">Channel Information</Label>
            <div className="bg-muted p-3 rounded-lg space-y-1">
              <p className="text-sm"><strong>Type:</strong> {source.type.toUpperCase()}</p>
              <p className="text-sm"><strong>URL:</strong> <span className="text-muted-foreground">{source.url}</span></p>
              {source.lastSync && (
                <p className="text-sm">
                  <strong>Last Sync:</strong> {new Date(source.lastSync).toLocaleString()}
                </p>
              )}
            </div>
          </div>

          {source.metadata && (
            <div className="space-y-2">
              <Label className="text-sm font-medium">Channel Stats</Label>
              <div className="bg-muted p-3 rounded-lg space-y-1">
                {source.metadata.subscriberCount && (
                  <p className="text-sm">
                    <strong>Subscribers:</strong> {source.metadata.subscriberCount.toLocaleString()}
                  </p>
                )}
                {source.metadata.videoCount && (
                  <p className="text-sm">
                    <strong>Videos:</strong> {source.metadata.videoCount.toLocaleString()}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-end space-x-2">
          <Button
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button onClick={handleSaveConfiguration} disabled={isLoading}>
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
