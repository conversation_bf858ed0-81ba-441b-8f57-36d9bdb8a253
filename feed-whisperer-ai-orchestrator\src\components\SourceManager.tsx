
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ContentSource } from '@/types/content';
import { AddSourceDialog } from './AddSourceDialog';
import { SourceConfigDialog } from './SourceConfigDialog';
import { SyncButton } from './SyncButton';
import { ExternalLink, Users, Video } from 'lucide-react';

interface SourceManagerProps {
  sources: ContentSource[];
  onSourceAdded?: () => void;
}

export const SourceManager: React.FC<SourceManagerProps> = ({ sources, onSourceAdded }) => {
  const getSourceIcon = (type: string) => {
    switch (type) {
      case 'youtube': return '📺';
      case 'instagram': return '📷';
      case 'gmail': return '📧';
      case 'rss': return '📡';
      default: return '🔗';
    }
  };

  const getSourceColor = (type: string) => {
    switch (type) {
      case 'youtube': return 'bg-red-100 text-red-800';
      case 'instagram': return 'bg-pink-100 text-pink-800';
      case 'gmail': return 'bg-blue-100 text-blue-800';
      case 'rss': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatNumber = (num: number | null | undefined) => {
    if (!num) return '0';
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Content Sources ({sources.length})</h2>
        <AddSourceDialog onSourceAdded={onSourceAdded}>
          <Button>Add New Source</Button>
        </AddSourceDialog>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {sources.map((source) => (
          <Card key={source.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-lg">{getSourceIcon(source.type)}</span>
                  <CardTitle className="text-base truncate">{source.name}</CardTitle>
                </div>
                <Badge 
                  variant={source.isActive ? 'default' : 'secondary'}
                  className={source.isActive ? 'bg-green-100 text-green-800' : ''}
                >
                  {source.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Badge className={getSourceColor(source.type)}>
                  {source.type.toUpperCase()}
                </Badge>
              </div>
              
              {source.metadata?.thumbnailUrl && (
                <div className="w-full h-20 bg-muted rounded overflow-hidden">
                  <img 
                    src={source.metadata.thumbnailUrl} 
                    alt={source.name}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}

              {/* Channel Stats */}
              {(source.metadata?.subscriberCount || source.metadata?.videoCount) && (
                <div className="flex space-x-4 text-sm text-muted-foreground">
                  {source.metadata.subscriberCount && (
                    <div className="flex items-center space-x-1">
                      <Users className="w-3 h-3" />
                      <span>{formatNumber(source.metadata.subscriberCount)}</span>
                    </div>
                  )}
                  {source.metadata.videoCount && (
                    <div className="flex items-center space-x-1">
                      <Video className="w-3 h-3" />
                      <span>{formatNumber(source.metadata.videoCount)}</span>
                    </div>
                  )}
                </div>
              )}
              
              <div className="text-sm text-muted-foreground">
                {source.lastSync && (
                  <p>Last sync: {new Date(source.lastSync).toLocaleDateString()}</p>
                )}
              </div>

              <div className="flex space-x-2">
                <SourceConfigDialog source={source} onConfigUpdated={onSourceAdded}>
                  <Button variant="outline" size="sm" className="flex-1">
                    Configure
                  </Button>
                </SourceConfigDialog>
                <SyncButton 
                  channelId={source.id} 
                  onSyncComplete={onSourceAdded}
                  size="sm"
                />
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => window.open(source.url, '_blank')}
                  className="flex items-center space-x-1"
                >
                  <ExternalLink className="w-3 h-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Add New Source Card */}
        <AddSourceDialog onSourceAdded={onSourceAdded}>
          <Card className="border-dashed border-2 hover:border-primary/50 transition-colors cursor-pointer">
            <CardContent className="flex flex-col items-center justify-center h-full min-h-[200px] space-y-3">
              <div className="text-4xl">➕</div>
              <div className="text-center">
                <h3 className="font-semibold">Add New Source</h3>
                <p className="text-sm text-muted-foreground">
                  Connect YouTube, Instagram, Gmail, or RSS feeds
                </p>
              </div>
              <Button variant="outline">Connect Source</Button>
            </CardContent>
          </Card>
        </AddSourceDialog>
      </div>
    </div>
  );
};
