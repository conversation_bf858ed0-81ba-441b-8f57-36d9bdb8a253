
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { channelId } = await req.json();
    
    console.log('Starting sync for channel:', channelId);

    // Get channel info
    const { data: channel, error: channelError } = await supabaseClient
      .from('youtube_channels')
      .select('*')
      .eq('id', channelId)
      .single();

    if (channelError || !channel) {
      throw new Error('Channel not found');
    }

    console.log('Channel data:', channel);
    console.log('RSS URL:', channel.rss_url);

    // Create sync log entry
    const { data: syncLog } = await supabaseClient
      .from('sync_logs')
      .insert({
        channel_id: channelId,
        sync_type: 'manual',
        status: 'running'
      })
      .select()
      .single();

    try {
      // Fetch RSS feed
      console.log('Fetching RSS feed:', channel.rss_url);
      const rssResponse = await fetch(channel.rss_url);
      
      if (!rssResponse.ok) {
        throw new Error(`RSS fetch failed: ${rssResponse.status} ${rssResponse.statusText}`);
      }
      
      const rssText = await rssResponse.text();
      console.log('RSS response length:', rssText.length);
      console.log('RSS response sample (first 500 chars):', rssText.substring(0, 500));

      // Parse RSS XML using regex (simple parsing for YouTube RSS)
      const entries = extractEntriesFromXML(rssText);

      let videosNew = 0;
      let videosFetched = entries.length;

      console.log(`Found ${videosFetched} videos in RSS feed`);

      for (const entry of entries) {
        if (!entry.videoId || !entry.title || !entry.publishedAt) continue;

        const videoUrl = `https://www.youtube.com/watch?v=${entry.videoId}`;

        // Check if video already exists
        const { data: existingVideo } = await supabaseClient
          .from('videos')
          .select('id')
          .eq('video_id', entry.videoId)
          .maybeSingle();

        if (!existingVideo) {
          // Insert new video
          const { error: insertError } = await supabaseClient
            .from('videos')
            .insert({
              channel_id: channelId,
              video_id: entry.videoId,
              title: entry.title,
              description: entry.description || '',
              video_url: videoUrl,
              thumbnail_url: entry.thumbnailUrl,
              published_at: entry.publishedAt
            });

          if (!insertError) {
            videosNew++;
            console.log('Inserted new video:', entry.title);
          } else {
            console.error('Error inserting video:', insertError);
          }
        }
      }

      // Update sync log
      await supabaseClient
        .from('sync_logs')
        .update({
          status: 'completed',
          videos_fetched: videosFetched,
          videos_new: videosNew,
          completed_at: new Date().toISOString()
        })
        .eq('id', syncLog?.id);

      // Update channel last fetched time
      await supabaseClient
        .from('youtube_channels')
        .update({
          last_fetched_at: new Date().toISOString()
        })
        .eq('id', channelId);

      console.log(`Sync completed: ${videosNew} new videos added out of ${videosFetched} total`);

      return new Response(JSON.stringify({ 
        success: true, 
        videosNew, 
        videosFetched,
        message: `Sync completed: ${videosNew} new videos added`,
        debug: {
          rssUrl: channel.rss_url,
          rssLength: rssText.length,
          entriesFound: entries.length
        }
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      });

    } catch (syncError) {
      console.error('Sync error:', syncError);
      
      // Update sync log with error
      await supabaseClient
        .from('sync_logs')
        .update({
          status: 'failed',
          error_message: syncError.message,
          completed_at: new Date().toISOString()
        })
        .eq('id', syncLog?.id);

      throw syncError;
    }

  } catch (error) {
    console.error('Error in sync-youtube-videos function:', error);
    return new Response(JSON.stringify({ 
      error: error.message 
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });
  }
});

// Helper function to extract entries from XML using regex
function extractEntriesFromXML(xmlText: string) {
  const entries = [];
  
  // Find all entry blocks
  const entryRegex = /<entry>([\s\S]*?)<\/entry>/g;
  let entryMatch;
  
  while ((entryMatch = entryRegex.exec(xmlText)) !== null) {
    const entryXml = entryMatch[1];
    
    // Extract video ID
    const videoIdMatch = entryXml.match(/<yt:videoId>(.*?)<\/yt:videoId>/) || 
                        entryXml.match(/<videoId>(.*?)<\/videoId>/);
    const videoId = videoIdMatch ? videoIdMatch[1] : null;
    
    // Extract title
    const titleMatch = entryXml.match(/<title>(.*?)<\/title>/);
    const title = titleMatch ? titleMatch[1] : null;
    
    // Extract published date
    const publishedMatch = entryXml.match(/<published>(.*?)<\/published>/);
    const publishedAt = publishedMatch ? publishedMatch[1] : null;
    
    // Extract description
    const descMatch = entryXml.match(/<media:description>(.*?)<\/media:description>/) ||
                     entryXml.match(/<description>(.*?)<\/description>/);
    const description = descMatch ? descMatch[1] : '';
    
    // Extract thumbnail
    const thumbnailMatch = entryXml.match(/<media:thumbnail[^>]*url="([^"]*)"/) ||
                          entryXml.match(/<thumbnail[^>]*url="([^"]*)"/);
    const thumbnailUrl = thumbnailMatch ? thumbnailMatch[1] : null;
    
    if (videoId && title && publishedAt) {
      entries.push({
        videoId,
        title: decodeXMLEntities(title),
        description: decodeXMLEntities(description),
        publishedAt,
        thumbnailUrl
      });
    }
  }
  
  return entries;
}

// Helper function to decode basic XML entities
function decodeXMLEntities(text: string): string {
  return text
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'");
}
