{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_myfeeds/youtube-feed-manager/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\n// Client-side Supabase client\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Client component client (for use in client components)\nexport const createClientSupabase = () => createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types\nexport interface Database {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string | null\n          full_name: string | null\n          avatar_url: string | null\n          preferences: any\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email?: string | null\n          full_name?: string | null\n          avatar_url?: string | null\n          preferences?: any\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string | null\n          full_name?: string | null\n          avatar_url?: string | null\n          preferences?: any\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      youtube_channels: {\n        Row: {\n          id: string\n          channel_id: string\n          channel_name: string\n          channel_url: string\n          rss_url: string\n          description: string | null\n          thumbnail_url: string | null\n          subscriber_count: number | null\n          video_count: number | null\n          last_fetched_at: string | null\n          is_active: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          channel_id: string\n          channel_name: string\n          channel_url: string\n          rss_url: string\n          description?: string | null\n          thumbnail_url?: string | null\n          subscriber_count?: number | null\n          video_count?: number | null\n          last_fetched_at?: string | null\n          is_active?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          channel_id?: string\n          channel_name?: string\n          channel_url?: string\n          rss_url?: string\n          description?: string | null\n          thumbnail_url?: string | null\n          subscriber_count?: number | null\n          video_count?: number | null\n          last_fetched_at?: string | null\n          is_active?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      user_subscriptions: {\n        Row: {\n          id: string\n          user_id: string\n          channel_id: string\n          subscription_name: string | null\n          tags: string[]\n          priority: number\n          is_active: boolean\n          notification_enabled: boolean\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          channel_id: string\n          subscription_name?: string | null\n          tags?: string[]\n          priority?: number\n          is_active?: boolean\n          notification_enabled?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          channel_id?: string\n          subscription_name?: string | null\n          tags?: string[]\n          priority?: number\n          is_active?: boolean\n          notification_enabled?: boolean\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      videos: {\n        Row: {\n          id: string\n          video_id: string\n          channel_id: string\n          title: string\n          description: string | null\n          thumbnail_url: string | null\n          duration: string | null\n          published_at: string\n          view_count: number | null\n          like_count: number | null\n          comment_count: number | null\n          video_url: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          video_id: string\n          channel_id: string\n          title: string\n          description?: string | null\n          thumbnail_url?: string | null\n          duration?: string | null\n          published_at: string\n          view_count?: number | null\n          like_count?: number | null\n          comment_count?: number | null\n          video_url: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          video_id?: string\n          channel_id?: string\n          title?: string\n          description?: string | null\n          thumbnail_url?: string | null\n          duration?: string | null\n          published_at?: string\n          view_count?: number | null\n          like_count?: number | null\n          comment_count?: number | null\n          video_url?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      user_video_interactions: {\n        Row: {\n          id: string\n          user_id: string\n          video_id: string\n          status: 'unread' | 'read' | 'saved' | 'hidden' | 'archived'\n          rating: number | null\n          notes: string | null\n          watched_at: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          video_id: string\n          status?: 'unread' | 'read' | 'saved' | 'hidden' | 'archived'\n          rating?: number | null\n          notes?: string | null\n          watched_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          video_id?: string\n          status?: 'unread' | 'read' | 'saved' | 'hidden' | 'archived'\n          rating?: number | null\n          notes?: string | null\n          watched_at?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      content_filters: {\n        Row: {\n          id: string\n          user_id: string\n          name: string\n          description: string | null\n          filter_type: 'include' | 'exclude'\n          conditions: any\n          is_active: boolean\n          priority: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          name: string\n          description?: string | null\n          filter_type: 'include' | 'exclude'\n          conditions: any\n          is_active?: boolean\n          priority?: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          name?: string\n          description?: string | null\n          filter_type?: 'include' | 'exclude'\n          conditions?: any\n          is_active?: boolean\n          priority?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAGC,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_myfeeds/youtube-feed-manager/src/components/Auth.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { createClientSupabase } from '@/lib/supabase'\n\nexport default function AuthComponent() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [isSignUp, setIsSignUp] = useState(false)\n  const [loading, setLoading] = useState(false)\n  const [message, setMessage] = useState('')\n  \n  const supabase = createClientSupabase()\n\n  const handleAuth = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setMessage('')\n\n    try {\n      if (isSignUp) {\n        const { error } = await supabase.auth.signUp({\n          email,\n          password,\n        })\n        if (error) throw error\n        setMessage('Check your email for the confirmation link!')\n      } else {\n        const { error } = await supabase.auth.signInWithPassword({\n          email,\n          password,\n        })\n        if (error) throw error\n      }\n    } catch (error: any) {\n      setMessage(error.message)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            YouTube Feed Manager\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            {isSignUp ? 'Create your account' : 'Sign in to your account'}\n          </p>\n        </div>\n        <form className=\"mt-8 space-y-6\" onSubmit={handleAuth}>\n          <div className=\"rounded-md shadow-sm -space-y-px\">\n            <div>\n              <input\n                type=\"email\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Email address\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n              />\n            </div>\n            <div>\n              <input\n                type=\"password\"\n                required\n                className=\"appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Password\"\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n              />\n            </div>\n          </div>\n\n          {message && (\n            <div className={`text-sm ${message.includes('error') || message.includes('Invalid') ? 'text-red-600' : 'text-green-600'}`}>\n              {message}\n            </div>\n          )}\n\n          <div>\n            <button\n              type=\"submit\"\n              disabled={loading}\n              className=\"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50\"\n            >\n              {loading ? 'Loading...' : (isSignUp ? 'Sign up' : 'Sign in')}\n            </button>\n          </div>\n\n          <div className=\"text-center\">\n            <button\n              type=\"button\"\n              className=\"text-blue-600 hover:text-blue-500\"\n              onClick={() => setIsSignUp(!isSignUp)}\n            >\n              {isSignUp ? 'Already have an account? Sign in' : \"Don't have an account? Sign up\"}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEpC,MAAM,aAAa,OAAO;QACxB,EAAE,cAAc;QAChB,WAAW;QACX,WAAW;QAEX,IAAI;YACF,IAAI,UAAU;gBACZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;oBAC3C;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;gBACjB,WAAW;YACb,OAAO;gBACL,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;oBACvD;oBACA;gBACF;gBACA,IAAI,OAAO,MAAM;YACnB;QACF,EAAE,OAAO,OAAY;YACnB,WAAW,MAAM,OAAO;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,8OAAC;4BAAE,WAAU;sCACV,WAAW,wBAAwB;;;;;;;;;;;;8BAGxC,8OAAC;oBAAK,WAAU;oBAAiB,UAAU;;sCACzC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CACC,cAAA,8OAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;8CAG5C,8OAAC;8CACC,cAAA,8OAAC;wCACC,MAAK;wCACL,QAAQ;wCACR,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;wBAKhD,yBACC,8OAAC;4BAAI,WAAW,CAAC,QAAQ,EAAE,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,aAAa,iBAAiB,kBAAkB;sCACtH;;;;;;sCAIL,8OAAC;sCACC,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAU;0CAET,UAAU,eAAgB,WAAW,YAAY;;;;;;;;;;;sCAItD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,YAAY,CAAC;0CAE3B,WAAW,qCAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/D", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_myfeeds/youtube-feed-manager/src/components/Dashboard.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { User } from '@supabase/supabase-js'\nimport { createClientSupabase } from '@/lib/supabase'\nimport { Plus, Youtube, Settings, LogOut } from 'lucide-react'\n\ninterface DashboardProps {\n  user: User\n}\n\nexport default function Dashboard({ user }: DashboardProps) {\n  const [subscriptions, setSubscriptions] = useState([])\n  const [loading, setLoading] = useState(true)\n  const supabase = createClientSupabase()\n\n  useEffect(() => {\n    fetchSubscriptions()\n  }, [])\n\n  const fetchSubscriptions = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('user_subscriptions')\n        .select(`\n          *,\n          youtube_channels (*)\n        `)\n        .eq('user_id', user.id)\n\n      if (error) throw error\n      setSubscriptions(data || [])\n    } catch (error) {\n      console.error('Error fetching subscriptions:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            <div className=\"flex items-center\">\n              <Youtube className=\"h-8 w-8 text-red-600 mr-3\" />\n              <h1 className=\"text-3xl font-bold text-gray-900\">YouTube Feed Manager</h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <span className=\"text-sm text-gray-700\">Welcome, {user.email}</span>\n              <button\n                onClick={handleSignOut}\n                className=\"inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500\"\n              >\n                <LogOut className=\"h-4 w-4 mr-2\" />\n                Sign out\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Quick Actions */}\n          <div className=\"mb-8\">\n            <h2 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Actions</h2>\n            <div className=\"grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4\">\n              <button className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow\">\n                <div>\n                  <span className=\"rounded-lg inline-flex p-3 bg-blue-50 text-blue-600 ring-4 ring-white\">\n                    <Plus className=\"h-6 w-6\" />\n                  </span>\n                </div>\n                <div className=\"mt-8\">\n                  <h3 className=\"text-lg font-medium\">\n                    <span className=\"absolute inset-0\" aria-hidden=\"true\" />\n                    Add Channel\n                  </h3>\n                  <p className=\"mt-2 text-sm text-gray-500\">\n                    Subscribe to a new YouTube channel\n                  </p>\n                </div>\n              </button>\n\n              <button className=\"relative group bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 rounded-lg shadow hover:shadow-md transition-shadow\">\n                <div>\n                  <span className=\"rounded-lg inline-flex p-3 bg-green-50 text-green-600 ring-4 ring-white\">\n                    <Settings className=\"h-6 w-6\" />\n                  </span>\n                </div>\n                <div className=\"mt-8\">\n                  <h3 className=\"text-lg font-medium\">\n                    <span className=\"absolute inset-0\" aria-hidden=\"true\" />\n                    Manage Filters\n                  </h3>\n                  <p className=\"mt-2 text-sm text-gray-500\">\n                    Set up content filtering rules\n                  </p>\n                </div>\n              </button>\n            </div>\n          </div>\n\n          {/* Subscriptions */}\n          <div>\n            <h2 className=\"text-lg font-medium text-gray-900 mb-4\">Your Subscriptions</h2>\n            {loading ? (\n              <div className=\"text-center py-12\">\n                <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n                <p className=\"mt-4 text-gray-500\">Loading subscriptions...</p>\n              </div>\n            ) : subscriptions.length === 0 ? (\n              <div className=\"text-center py-12\">\n                <Youtube className=\"mx-auto h-12 w-12 text-gray-400\" />\n                <h3 className=\"mt-2 text-sm font-medium text-gray-900\">No subscriptions</h3>\n                <p className=\"mt-1 text-sm text-gray-500\">Get started by adding your first YouTube channel.</p>\n                <div className=\"mt-6\">\n                  <button className=\"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\">\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Add Channel\n                  </button>\n                </div>\n              </div>\n            ) : (\n              <div className=\"bg-white shadow overflow-hidden sm:rounded-md\">\n                <ul className=\"divide-y divide-gray-200\">\n                  {subscriptions.map((subscription: any) => (\n                    <li key={subscription.id}>\n                      <div className=\"px-4 py-4 flex items-center justify-between\">\n                        <div className=\"flex items-center\">\n                          <div className=\"flex-shrink-0\">\n                            <img\n                              className=\"h-10 w-10 rounded-full\"\n                              src={subscription.youtube_channels?.thumbnail_url || '/placeholder-channel.png'}\n                              alt={subscription.youtube_channels?.channel_name}\n                            />\n                          </div>\n                          <div className=\"ml-4\">\n                            <div className=\"text-sm font-medium text-gray-900\">\n                              {subscription.youtube_channels?.channel_name}\n                            </div>\n                            <div className=\"text-sm text-gray-500\">\n                              {subscription.subscription_name || 'No custom name'}\n                            </div>\n                          </div>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${\n                            subscription.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'\n                          }`}>\n                            {subscription.is_active ? 'Active' : 'Inactive'}\n                          </span>\n                        </div>\n                      </div>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAAA;AAAA;AAAA;AALA;;;;;AAWe,SAAS,UAAU,EAAE,IAAI,EAAkB;IACxD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,sBACL,MAAM,CAAC,CAAC;;;QAGT,CAAC,EACA,EAAE,CAAC,WAAW,KAAK,EAAE;YAExB,IAAI,OAAO,MAAM;YACjB,iBAAiB,QAAQ,EAAE;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,wMAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,8OAAC;wCAAG,WAAU;kDAAmC;;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAwB;4CAAU,KAAK,KAAK;;;;;;;kDAC5D,8OAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;8DACC,cAAA,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAK,WAAU;oEAAmB,eAAY;;;;;;gEAAS;;;;;;;sEAG1D,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAM9C,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;8DACC,cAAA,8OAAC;wDAAK,WAAU;kEACd,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAGxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEAAK,WAAU;oEAAmB,eAAY;;;;;;gEAAS;;;;;;;sEAG1D,8OAAC;4DAAE,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASlD,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;gCACtD,wBACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;;;;;;2CAElC,cAAc,MAAM,KAAK,kBAC3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,wMAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;sDACnB,8OAAC;4CAAG,WAAU;sDAAyC;;;;;;sDACvD,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAC1C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;yDAMvC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDACX,cAAc,GAAG,CAAC,CAAC,6BAClB,8OAAC;0DACC,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,WAAU;wEACV,KAAK,aAAa,gBAAgB,EAAE,iBAAiB;wEACrD,KAAK,aAAa,gBAAgB,EAAE;;;;;;;;;;;8EAGxC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;sFACZ,aAAa,gBAAgB,EAAE;;;;;;sFAElC,8OAAC;4EAAI,WAAU;sFACZ,aAAa,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;sEAIzC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAW,CAAC,wEAAwE,EACxF,aAAa,SAAS,GAAG,gCAAgC,6BACzD;0EACC,aAAa,SAAS,GAAG,WAAW;;;;;;;;;;;;;;;;;+CAvBpC,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqC5C", "debugId": null}}, {"offset": {"line": 802, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_myfeeds/youtube-feed-manager/src/app/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { createClientSupabase } from '@/lib/supabase'\r\nimport { User } from '@supabase/supabase-js'\r\nimport AuthComponent from '@/components/Auth'\r\nimport Dashboard from '@/components/Dashboard'\r\n\r\nexport default function Home() {\r\n  const [user, setUser] = useState<User | null>(null)\r\n  const [loading, setLoading] = useState(true)\r\n  const supabase = createClientSupabase()\r\n\r\n  useEffect(() => {\r\n    // Get initial session\r\n    const getSession = async () => {\r\n      const { data: { session } } = await supabase.auth.getSession()\r\n      setUser(session?.user ?? null)\r\n      setLoading(false)\r\n    }\r\n\r\n    getSession()\r\n\r\n    // Listen for auth changes\r\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\r\n      async (event, session) => {\r\n        setUser(session?.user ?? null)\r\n        setLoading(false)\r\n      }\r\n    )\r\n\r\n    return () => subscription.unsubscribe()\r\n  }, [supabase.auth])\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (!user) {\r\n    return <AuthComponent />\r\n  }\r\n\r\n  return <Dashboard user={user} />\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;QACtB,MAAM,aAAa;YACjB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YAC5D,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC,SAAS,IAAI;KAAC;IAElB,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,qBAAO,8OAAC,0HAAA,CAAA,UAAa;;;;;IACvB;IAEA,qBAAO,8OAAC,+HAAA,CAAA,UAAS;QAAC,MAAM;;;;;;AAC1B", "debugId": null}}]}