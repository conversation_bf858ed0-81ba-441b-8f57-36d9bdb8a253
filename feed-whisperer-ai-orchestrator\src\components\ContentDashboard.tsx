
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ContentSource, Content, ContentType } from '@/types/content';
import { SourceManager } from './SourceManager';
import { ContentList } from './ContentList';
import { FilterPanel } from './FilterPanel';
import { AddSourceDialog } from './AddSourceDialog';
import { useContentData } from '@/hooks/useContentData';

export const ContentDashboard = () => {
  const [activeTab, setActiveTab] = useState<'sources' | 'content' | 'filters'>('sources');
  const { sources, content, stats, isLoading, refetch } = useContentData();

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading your content...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Content Pipeline</h1>
            <p className="text-muted-foreground">
              Unified content management across all your digital channels
            </p>
          </div>
          <AddSourceDialog onSourceAdded={refetch}>
            <Button variant="outline">Connect New Source</Button>
          </AddSourceDialog>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalSources}</div>
              <p className="text-xs text-muted-foreground">
                {stats.activeSources} active
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Unread Content</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.unreadContent}</div>
              <p className="text-xs text-muted-foreground">
                Requires attention
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">High Priority</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.highPriority}</div>
              <p className="text-xs text-muted-foreground">
                Score &gt; 7.0
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Processing Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {sources.length > 0 ? Math.round((stats.activeSources / stats.totalSources) * 100) : 0}%
              </div>
              <p className="text-xs text-muted-foreground">
                Active sources
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
          {[
            { key: 'sources', label: 'Sources' },
            { key: 'content', label: 'Content' },
            { key: 'filters', label: 'Rules & Filters' }
          ].map((tab) => (
            <Button
              key={tab.key}
              variant={activeTab === tab.key ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveTab(tab.key as any)}
            >
              {tab.label}
            </Button>
          ))}
        </div>

        {/* Content Area */}
        <div className="space-y-6">
          {activeTab === 'sources' && <SourceManager sources={sources} onSourceAdded={refetch} />}
          {activeTab === 'content' && <ContentList content={content} />}
          {activeTab === 'filters' && <FilterPanel />}
        </div>
      </div>
    </div>
  );
};
