
export type ContentSourceType = 'youtube' | 'instagram' | 'gmail' | 'rss' | 'custom';

export type ContentType = 'video' | 'post' | 'email' | 'article' | 'image';

export interface ContentSource {
  id: string;
  name: string;
  type: ContentSourceType;
  url: string;
  isActive: boolean;
  lastSync?: string;
  metadata?: Record<string, any>;
}

export interface Content {
  id: string;
  sourceId: string;
  title: string;
  description?: string;
  type: ContentType;
  url: string;
  thumbnail?: string;
  publishedAt: string;
  metadata: Record<string, any>;
  priorityScore: number;
  tags: string[];
  isRead: boolean;
  isFavorite: boolean;
}

export interface FilterRule {
  id: string;
  name: string;
  sourceTypes: ContentSourceType[];
  keywords: string[];
  excludeKeywords: string[];
  minPriorityScore: number;
  actions: RuleAction[];
  isActive: boolean;
}

export interface RuleAction {
  type: 'tag' | 'priority' | 'notify' | 'archive' | 'forward';
  value: string | number;
}
