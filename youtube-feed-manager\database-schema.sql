-- YouTube Feed Manager Database Schema
-- This schema supports comprehensive YouTube subscription management with intelligent filtering

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT,
    full_name TEXT,
    avatar_url TEXT,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- YouTube channels table
CREATE TABLE public.youtube_channels (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    channel_id TEXT UNIQUE NOT NULL, -- YouTube channel ID
    channel_name TEXT NOT NULL,
    channel_url TEXT NOT NULL,
    rss_url TEXT NOT NULL,
    description TEXT,
    thumbnail_url TEXT,
    subscriber_count INTEGER,
    video_count INTEGER,
    last_fetched_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User subscriptions to channels
CREATE TABLE public.user_subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    channel_id UUID REFERENCES public.youtube_channels(id) ON DELETE CASCADE,
    subscription_name TEXT, -- Custom name for the subscription
    tags TEXT[] DEFAULT '{}', -- User-defined tags
    priority INTEGER DEFAULT 1, -- 1=low, 2=medium, 3=high
    is_active BOOLEAN DEFAULT true,
    notification_enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, channel_id)
);

-- Videos table
CREATE TABLE public.videos (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    video_id TEXT UNIQUE NOT NULL, -- YouTube video ID
    channel_id UUID REFERENCES public.youtube_channels(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    thumbnail_url TEXT,
    duration TEXT, -- ISO 8601 duration format
    published_at TIMESTAMP WITH TIME ZONE NOT NULL,
    view_count INTEGER,
    like_count INTEGER,
    comment_count INTEGER,
    video_url TEXT NOT NULL,
    embedding VECTOR(1536), -- For semantic search
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User video interactions
CREATE TABLE public.user_video_interactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    video_id UUID REFERENCES public.videos(id) ON DELETE CASCADE,
    status TEXT CHECK (status IN ('unread', 'read', 'saved', 'hidden', 'archived')) DEFAULT 'unread',
    rating INTEGER CHECK (rating >= 1 AND rating <= 5), -- User rating
    notes TEXT,
    watched_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, video_id)
);

-- Content filters
CREATE TABLE public.content_filters (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    filter_type TEXT CHECK (filter_type IN ('include', 'exclude')) NOT NULL,
    conditions JSONB NOT NULL, -- Filter conditions (keywords, duration, etc.)
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Filter applications log
CREATE TABLE public.filter_applications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    filter_id UUID REFERENCES public.content_filters(id) ON DELETE CASCADE,
    video_id UUID REFERENCES public.videos(id) ON DELETE CASCADE,
    action_taken TEXT NOT NULL, -- 'included', 'excluded', 'tagged'
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Feed sync logs
CREATE TABLE public.sync_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    channel_id UUID REFERENCES public.youtube_channels(id) ON DELETE CASCADE,
    sync_type TEXT CHECK (sync_type IN ('manual', 'scheduled', 'webhook')) NOT NULL,
    status TEXT CHECK (status IN ('success', 'error', 'partial')) NOT NULL,
    videos_fetched INTEGER DEFAULT 0,
    videos_new INTEGER DEFAULT 0,
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create indexes for performance
CREATE INDEX idx_youtube_channels_channel_id ON public.youtube_channels(channel_id);
CREATE INDEX idx_videos_channel_id ON public.videos(channel_id);
CREATE INDEX idx_videos_published_at ON public.videos(published_at DESC);
CREATE INDEX idx_videos_video_id ON public.videos(video_id);
CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX idx_user_video_interactions_user_id ON public.user_video_interactions(user_id);
CREATE INDEX idx_user_video_interactions_status ON public.user_video_interactions(status);
CREATE INDEX idx_content_filters_user_id ON public.content_filters(user_id);
CREATE INDEX idx_sync_logs_channel_id ON public.sync_logs(channel_id);

-- Vector similarity search index
CREATE INDEX ON public.videos USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Row Level Security (RLS) policies
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_video_interactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.content_filters ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- User subscriptions policies
CREATE POLICY "Users can manage own subscriptions" ON public.user_subscriptions
    FOR ALL USING (auth.uid() = user_id);

-- User video interactions policies
CREATE POLICY "Users can manage own video interactions" ON public.user_video_interactions
    FOR ALL USING (auth.uid() = user_id);

-- Content filters policies
CREATE POLICY "Users can manage own filters" ON public.content_filters
    FOR ALL USING (auth.uid() = user_id);

-- Public read access for channels and videos (needed for RSS fetching)
CREATE POLICY "Public read access for channels" ON public.youtube_channels
    FOR SELECT USING (true);

CREATE POLICY "Public read access for videos" ON public.videos
    FOR SELECT USING (true);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply update triggers
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_youtube_channels_updated_at BEFORE UPDATE ON public.youtube_channels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at BEFORE UPDATE ON public.user_subscriptions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_videos_updated_at BEFORE UPDATE ON public.videos
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_video_interactions_updated_at BEFORE UPDATE ON public.user_video_interactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_filters_updated_at BEFORE UPDATE ON public.content_filters
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
