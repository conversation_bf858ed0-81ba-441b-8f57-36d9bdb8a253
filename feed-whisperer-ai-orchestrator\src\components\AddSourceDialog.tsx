
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Plus } from 'lucide-react';

interface AddSourceDialogProps {
  onSourceAdded?: () => void;
  children?: React.ReactNode;
}

export const AddSourceDialog: React.FC<AddSourceDialogProps> = ({ 
  onSourceAdded,
  children 
}) => {
  const [open, setOpen] = useState(false);
  const [channelUrl, setChannelUrl] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const extractChannelId = (url: string): string | null => {
    // Handle various YouTube URL formats
    const patterns = [
      /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/,
      /youtube\.com\/c\/([a-zA-Z0-9_-]+)/,
      /youtube\.com\/user\/([a-zA-Z0-9_-]+)/,
      /youtube\.com\/@([a-zA-Z0-9_-]+)/,
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  };

  const handleAddSource = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!channelUrl.trim()) {
      toast({
        title: "Error",
        description: "Please enter a YouTube channel URL",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const channelId = extractChannelId(channelUrl);
      if (!channelId) {
        toast({
          title: "Invalid URL",
          description: "Please enter a valid YouTube channel URL",
          variant: "destructive",
        });
        return;
      }

      // Create a basic channel entry
      const channelData = {
        channel_id: channelId,
        channel_name: `Channel ${channelId}`, // Will be updated when we fetch real data
        channel_url: channelUrl,
        rss_url: `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`,
        description: '',
        thumbnail_url: '',
        subscriber_count: null,
        video_count: null,
        is_active: true,
      };

      const { error } = await supabase
        .from('youtube_channels')
        .insert([channelData]);

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          toast({
            title: "Channel already exists",
            description: "This YouTube channel is already added to your sources",
            variant: "destructive",
          });
        } else {
          throw error;
        }
        return;
      }

      toast({
        title: "Success",
        description: "YouTube channel added successfully!",
      });

      setChannelUrl('');
      setOpen(false);
      onSourceAdded?.();
    } catch (error) {
      console.error('Error adding channel:', error);
      toast({
        title: "Error",
        description: "Failed to add YouTube channel. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Connect New Source
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add YouTube Channel</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleAddSource} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="channelUrl">YouTube Channel URL</Label>
            <Input
              id="channelUrl"
              type="url"
              placeholder="https://youtube.com/@channelname"
              value={channelUrl}
              onChange={(e) => setChannelUrl(e.target.value)}
              disabled={isLoading}
            />
            <p className="text-sm text-muted-foreground">
              Supported formats: @username, /c/channel, /channel/ID, /user/username
            </p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Adding...' : 'Add Channel'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
