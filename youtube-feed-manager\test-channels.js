#!/usr/bin/env node

/**
 * Test the simple fetcher with some popular channels
 */

const { fetchChannelVideos } = require('./simple-fetcher');

// Test channels (using actual channel IDs that work with RSS)
const TEST_CHANNELS = [
  'https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA', // Fireship
  'https://www.youtube.com/feeds/videos.xml?channel_id=UC8ENHE5xdFSwx71u3fDH5Xw', // ThePrimeTimeagen
  'https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA'  // Vercel
];

async function testFetcher() {
  console.log('🧪 Testing YouTube RSS Fetcher with sample channels...\n');
  
  for (const channel of TEST_CHANNELS) {
    console.log(`Testing: ${channel}`);
    const videos = await fetchChannelVideos(channel);
    
    if (videos.length > 0) {
      console.log(`✅ Success! Found ${videos.length} videos`);
      console.log(`   Latest: "${videos[0].title}"`);
      console.log(`   Published: ${videos[0].publishedAt}`);
    } else {
      console.log(`❌ No videos found`);
    }
    console.log('');
    
    // Small delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('🎉 Test complete!');
}

if (require.main === module) {
  testFetcher().catch(console.error);
}
