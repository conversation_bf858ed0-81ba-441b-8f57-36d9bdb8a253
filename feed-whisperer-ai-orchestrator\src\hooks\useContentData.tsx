
import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { ContentSource, Content } from '@/types/content';

export const useContentData = () => {
  // Fetch YouTube channels as content sources
  const { data: channels = [], isLoading: channelsLoading, refetch: refetchChannels } = useQuery({
    queryKey: ['youtube-channels'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('youtube_channels')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    }
  });

  // Fetch videos as content
  const { data: videos = [], isLoading: videosLoading, refetch: refetchVideos } = useQuery({
    queryKey: ['videos'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('videos')
        .select(`
          *,
          youtube_channels(channel_name, thumbnail_url)
        `)
        .order('published_at', { ascending: false })
        .limit(50);
      
      if (error) throw error;
      return data;
    }
  });

  // Transform channels to ContentSource format
  const sources: ContentSource[] = channels.map(channel => ({
    id: channel.id,
    name: channel.channel_name,
    type: 'youtube',
    url: channel.channel_url,
    isActive: channel.is_active,
    lastSync: channel.last_fetched_at,
    metadata: {
      subscriberCount: channel.subscriber_count,
      videoCount: channel.video_count,
      thumbnailUrl: channel.thumbnail_url
    }
  }));

  // Transform videos to Content format
  const content: Content[] = videos.map(video => ({
    id: video.id,
    sourceId: video.channel_id,
    title: video.title,
    description: video.description,
    type: 'video',
    url: video.video_url,
    thumbnail: video.thumbnail_url,
    publishedAt: video.published_at,
    metadata: {
      duration: video.duration,
      views: video.view_count,
      likes: video.like_count,
      comments: video.comment_count,
      channelName: video.youtube_channels?.channel_name
    },
    priorityScore: calculatePriorityScore(video),
    tags: extractTags(video.title, video.description),
    isRead: false, // Will be handled by user interactions table
    isFavorite: false
  }));

  const isLoading = channelsLoading || videosLoading;

  const stats = {
    totalSources: sources.length,
    activeSources: sources.filter(s => s.isActive).length,
    unreadContent: content.length, // All content is unread for now
    highPriority: content.filter(c => c.priorityScore > 7).length
  };

  const refetch = () => {
    refetchChannels();
    refetchVideos();
  };

  return {
    sources,
    content,
    stats,
    isLoading,
    refetch
  };
};

// Helper function to calculate priority score based on engagement
function calculatePriorityScore(video: any): number {
  const views = video.view_count || 0;
  const likes = video.like_count || 0;
  const comments = video.comment_count || 0;
  
  // Simple scoring algorithm - can be enhanced
  let score = 5; // Base score
  
  if (views > 100000) score += 2;
  if (views > 1000000) score += 1;
  
  if (likes > 1000) score += 1;
  if (comments > 100) score += 1;
  
  // Boost recent content
  const daysSincePublished = (Date.now() - new Date(video.published_at).getTime()) / (1000 * 60 * 60 * 24);
  if (daysSincePublished < 1) score += 2;
  else if (daysSincePublished < 7) score += 1;
  
  return Math.min(10, Math.max(1, score));
}

// Helper function to extract tags from title and description
function extractTags(title: string, description: string): string[] {
  const text = `${title} ${description || ''}`.toLowerCase();
  const commonTags = ['ai', 'machine learning', 'tech', 'tutorial', 'review', 'news', 'programming', 'coding'];
  
  return commonTags.filter(tag => text.includes(tag));
}
