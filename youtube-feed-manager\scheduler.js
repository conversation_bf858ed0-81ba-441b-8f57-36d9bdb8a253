#!/usr/bin/env node

/**
 * Simple Scheduler for YouTube RSS Fetcher
 * 
 * Runs the fetcher at regular intervals
 */

const { main } = require('./simple-fetcher');

// Configuration
const INTERVAL_MINUTES = 30; // How often to check for new videos
const INTERVAL_MS = INTERVAL_MINUTES * 60 * 1000;

console.log(`🕐 YouTube RSS Scheduler started`);
console.log(`   Checking every ${INTERVAL_MINUTES} minutes`);
console.log(`   Press Ctrl+C to stop\n`);

// Run immediately
console.log('🚀 Running initial fetch...');
main().catch(console.error);

// Then run on schedule
setInterval(() => {
  console.log(`\n⏰ Scheduled fetch at ${new Date().toLocaleString()}`);
  main().catch(console.error);
}, INTERVAL_MS);

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n👋 Scheduler stopped');
  process.exit(0);
});
