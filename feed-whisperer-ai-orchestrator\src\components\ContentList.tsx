
import React from 'react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Content } from '@/types/content';
import { ExternalLink, Clock, Eye, ThumbsUp, MessageCircle } from 'lucide-react';

interface ContentListProps {
  content: Content[];
}

export const ContentList: React.FC<ContentListProps> = ({ content }) => {
  const getContentIcon = (type: string) => {
    switch (type) {
      case 'video': return '🎥';
      case 'post': return '📄';
      case 'email': return '📧';
      case 'article': return '📰';
      case 'image': return '🖼️';
      default: return '📋';
    }
  };

  const getPriorityColor = (score: number) => {
    if (score >= 8) return 'bg-red-100 text-red-800';
    if (score >= 6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-green-100 text-green-800';
  };

  const getPriorityLabel = (score: number) => {
    if (score >= 8) return 'High';
    if (score >= 6) return 'Medium';
    return 'Low';
  };

  const formatNumber = (num: number | null | undefined) => {
    if (!num) return '0';
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Content Feed ({content.length})</h2>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">Filter</Button>
          <Button variant="outline" size="sm">Sort</Button>
          <Button size="sm">Mark All Read</Button>
        </div>
      </div>

      {content.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="text-4xl mb-4">📭</div>
            <h3 className="text-lg font-semibold mb-2">No content yet</h3>
            <p className="text-muted-foreground">
              Connect some sources to start seeing content here.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {content.map((item) => (
            <Card key={item.id} className={`hover:shadow-md transition-shadow ${!item.isRead ? 'ring-1 ring-primary/20' : ''}`}>
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between space-x-4">
                  <div className="flex items-start space-x-3 flex-1">
                    {item.thumbnail && (
                      <img 
                        src={item.thumbnail} 
                        alt={item.title}
                        className="w-24 h-16 object-cover rounded flex-shrink-0"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-lg leading-tight line-clamp-2">
                        {item.title}
                      </h3>
                      {item.description && (
                        <p className="text-muted-foreground text-sm mt-1 line-clamp-2">
                          {item.description}
                        </p>
                      )}
                      {item.metadata.channelName && (
                        <p className="text-sm text-muted-foreground mt-1">
                          by {item.metadata.channelName}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 shrink-0">
                    <Badge className={getPriorityColor(item.priorityScore)}>
                      {getPriorityLabel(item.priorityScore)} ({item.priorityScore})
                    </Badge>
                    {!item.isRead && (
                      <div className="w-2 h-2 bg-primary rounded-full"></div>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                    <span>{formatDate(item.publishedAt)}</span>
                    {item.metadata.duration && (
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{item.metadata.duration}</span>
                      </div>
                    )}
                    {item.metadata.views && (
                      <div className="flex items-center space-x-1">
                        <Eye className="w-3 h-3" />
                        <span>{formatNumber(item.metadata.views)}</span>
                      </div>
                    )}
                    {item.metadata.likes && (
                      <div className="flex items-center space-x-1">
                        <ThumbsUp className="w-3 h-3" />
                        <span>{formatNumber(item.metadata.likes)}</span>
                      </div>
                    )}
                    {item.metadata.comments && (
                      <div className="flex items-center space-x-1">
                        <MessageCircle className="w-3 h-3" />
                        <span>{formatNumber(item.metadata.comments)}</span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {item.tags.slice(0, 3).map((tag) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open(item.url, '_blank')}
                    className="flex items-center space-x-1"
                  >
                    <ExternalLink className="w-3 h-3" />
                    <span>Open</span>
                  </Button>
                  <Button variant="outline" size="sm">
                    {item.isRead ? 'Mark Unread' : 'Mark Read'}
                  </Button>
                  <Button variant="outline" size="sm">
                    {item.isFavorite ? '❤️' : '🤍'}
                  </Button>
                  <Button variant="outline" size="sm">
                    Archive
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};
