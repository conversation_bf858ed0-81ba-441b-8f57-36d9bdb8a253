export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      content_filters: {
        Row: {
          conditions: <PERSON><PERSON>
          created_at: string | null
          description: string | null
          filter_type: string
          id: string
          is_active: boolean | null
          name: string
          priority: number | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          conditions: Json
          created_at?: string | null
          description?: string | null
          filter_type: string
          id?: string
          is_active?: boolean | null
          name: string
          priority?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          conditions?: J<PERSON>
          created_at?: string | null
          description?: string | null
          filter_type?: string
          id?: string
          is_active?: boolean | null
          name?: string
          priority?: number | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "content_filters_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      filter_applications: {
        Row: {
          action_taken: string
          applied_at: string | null
          filter_id: string | null
          id: string
          video_id: string | null
        }
        Insert: {
          action_taken: string
          applied_at?: string | null
          filter_id?: string | null
          id?: string
          video_id?: string | null
        }
        Update: {
          action_taken?: string
          applied_at?: string | null
          filter_id?: string | null
          id?: string
          video_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "filter_applications_filter_id_fkey"
            columns: ["filter_id"]
            isOneToOne: false
            referencedRelation: "content_filters"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "filter_applications_video_id_fkey"
            columns: ["video_id"]
            isOneToOne: false
            referencedRelation: "videos"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string | null
          email: string | null
          full_name: string | null
          id: string
          preferences: Json | null
          updated_at: string | null
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id: string
          preferences?: Json | null
          updated_at?: string | null
        }
        Update: {
          avatar_url?: string | null
          created_at?: string | null
          email?: string | null
          full_name?: string | null
          id?: string
          preferences?: Json | null
          updated_at?: string | null
        }
        Relationships: []
      }
      sync_logs: {
        Row: {
          channel_id: string | null
          completed_at: string | null
          error_message: string | null
          id: string
          started_at: string | null
          status: string
          sync_type: string
          videos_fetched: number | null
          videos_new: number | null
        }
        Insert: {
          channel_id?: string | null
          completed_at?: string | null
          error_message?: string | null
          id?: string
          started_at?: string | null
          status: string
          sync_type: string
          videos_fetched?: number | null
          videos_new?: number | null
        }
        Update: {
          channel_id?: string | null
          completed_at?: string | null
          error_message?: string | null
          id?: string
          started_at?: string | null
          status?: string
          sync_type?: string
          videos_fetched?: number | null
          videos_new?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "sync_logs_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "youtube_channels"
            referencedColumns: ["id"]
          },
        ]
      }
      user_subscriptions: {
        Row: {
          channel_id: string | null
          created_at: string | null
          id: string
          is_active: boolean | null
          notification_enabled: boolean | null
          priority: number | null
          subscription_name: string | null
          tags: string[] | null
          updated_at: string | null
          user_id: string | null
        }
        Insert: {
          channel_id?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          notification_enabled?: boolean | null
          priority?: number | null
          subscription_name?: string | null
          tags?: string[] | null
          updated_at?: string | null
          user_id?: string | null
        }
        Update: {
          channel_id?: string | null
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          notification_enabled?: boolean | null
          priority?: number | null
          subscription_name?: string | null
          tags?: string[] | null
          updated_at?: string | null
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_subscriptions_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "youtube_channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_subscriptions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_video_interactions: {
        Row: {
          created_at: string | null
          id: string
          notes: string | null
          rating: number | null
          status: string | null
          updated_at: string | null
          user_id: string | null
          video_id: string | null
          watched_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          notes?: string | null
          rating?: number | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
          video_id?: string | null
          watched_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          notes?: string | null
          rating?: number | null
          status?: string | null
          updated_at?: string | null
          user_id?: string | null
          video_id?: string | null
          watched_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_video_interactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_video_interactions_video_id_fkey"
            columns: ["video_id"]
            isOneToOne: false
            referencedRelation: "videos"
            referencedColumns: ["id"]
          },
        ]
      }
      videos: {
        Row: {
          channel_id: string | null
          comment_count: number | null
          created_at: string | null
          description: string | null
          duration: string | null
          id: string
          like_count: number | null
          published_at: string
          thumbnail_url: string | null
          title: string
          updated_at: string | null
          video_id: string
          video_url: string
          view_count: number | null
        }
        Insert: {
          channel_id?: string | null
          comment_count?: number | null
          created_at?: string | null
          description?: string | null
          duration?: string | null
          id?: string
          like_count?: number | null
          published_at: string
          thumbnail_url?: string | null
          title: string
          updated_at?: string | null
          video_id: string
          video_url: string
          view_count?: number | null
        }
        Update: {
          channel_id?: string | null
          comment_count?: number | null
          created_at?: string | null
          description?: string | null
          duration?: string | null
          id?: string
          like_count?: number | null
          published_at?: string
          thumbnail_url?: string | null
          title?: string
          updated_at?: string | null
          video_id?: string
          video_url?: string
          view_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "videos_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "youtube_channels"
            referencedColumns: ["id"]
          },
        ]
      }
      youtube_channels: {
        Row: {
          channel_id: string
          channel_name: string
          channel_url: string
          created_at: string | null
          description: string | null
          id: string
          is_active: boolean | null
          last_fetched_at: string | null
          rss_url: string
          subscriber_count: number | null
          thumbnail_url: string | null
          updated_at: string | null
          video_count: number | null
        }
        Insert: {
          channel_id: string
          channel_name: string
          channel_url: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          last_fetched_at?: string | null
          rss_url: string
          subscriber_count?: number | null
          thumbnail_url?: string | null
          updated_at?: string | null
          video_count?: number | null
        }
        Update: {
          channel_id?: string
          channel_name?: string
          channel_url?: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_active?: boolean | null
          last_fetched_at?: string | null
          rss_url?: string
          subscriber_count?: number | null
          thumbnail_url?: string | null
          updated_at?: string | null
          video_count?: number | null
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
