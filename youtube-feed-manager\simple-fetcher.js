#!/usr/bin/env node

/**
 * Ultra-Simple YouTube RSS Fetcher
 * 
 * Usage:
 * 1. Add your channel URLs to the channels array below
 * 2. Run: node simple-fetcher.js
 * 3. Videos are saved to videos.json
 */

const fs = require('fs');
const https = require('https');
const { parseString } = require('xml2js');

// ===== CONFIGURATION =====
const CHANNELS = [
  // Test with some working channels
  'https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA', // Fireship
  'https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA'  // Vercel
];

const OUTPUT_FILE = 'videos.json';
const MAX_VIDEOS_PER_CHANNEL = 15; // YouTube RSS typically returns 15 videos

// ===== HELPER FUNCTIONS =====

// Extract channel ID from various YouTube URL formats
function extractChannelId(url) {
  const patterns = [
    /youtube\.com\/channel\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/c\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/user\/([a-zA-Z0-9_-]+)/,
    /youtube\.com\/@([a-zA-Z0-9_-]+)/,
    /feeds\/videos\.xml\?channel_id=([a-zA-Z0-9_-]+)/
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) return match[1];
  }
  return null;
}

// Convert channel URL to RSS URL
function getRSSUrl(channelUrl) {
  if (channelUrl.includes('feeds/videos.xml')) {
    return channelUrl; // Already an RSS URL
  }
  
  const channelId = extractChannelId(channelUrl);
  if (!channelId) {
    throw new Error(`Could not extract channel ID from: ${channelUrl}`);
  }
  
  return `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`;
}

// Simple HTTP GET request
function httpGet(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          resolve(data);
        } else {
          reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
        }
      });
    }).on('error', reject);
  });
}

// Parse XML to JSON
function parseXML(xml) {
  return new Promise((resolve, reject) => {
    parseString(xml, { explicitArray: false }, (err, result) => {
      if (err) reject(err);
      else resolve(result);
    });
  });
}

// Load existing videos
function loadExistingVideos() {
  try {
    if (fs.existsSync(OUTPUT_FILE)) {
      const data = fs.readFileSync(OUTPUT_FILE, 'utf8');
      return JSON.parse(data);
    }
  } catch (error) {
    console.warn('Could not load existing videos:', error.message);
  }
  return [];
}

// Save videos to file
function saveVideos(videos) {
  try {
    fs.writeFileSync(OUTPUT_FILE, JSON.stringify(videos, null, 2));
    console.log(`✅ Saved ${videos.length} videos to ${OUTPUT_FILE}`);
  } catch (error) {
    console.error('❌ Error saving videos:', error.message);
  }
}

// ===== MAIN FETCHER FUNCTION =====

async function fetchChannelVideos(channelUrl) {
  try {
    console.log(`📡 Fetching: ${channelUrl}`);
    
    const rssUrl = getRSSUrl(channelUrl);
    console.log(`   RSS URL: ${rssUrl}`);
    
    const xmlData = await httpGet(rssUrl);
    const parsed = await parseXML(xmlData);
    
    const feed = parsed.feed;
    if (!feed || !feed.entry) {
      console.log(`   ⚠️  No videos found`);
      return [];
    }
    
    const entries = Array.isArray(feed.entry) ? feed.entry : [feed.entry];
    const videos = entries.map(entry => ({
      id: entry['yt:videoId'] || entry.id?.split(':').pop(),
      title: entry.title,
      description: entry['media:group']?.['media:description'] || '',
      url: `https://www.youtube.com/watch?v=${entry['yt:videoId'] || entry.id?.split(':').pop()}`,
      thumbnail: entry['media:group']?.['media:thumbnail']?.$.url || `https://img.youtube.com/vi/${entry['yt:videoId']}/maxresdefault.jpg`,
      publishedAt: entry.published,
      channelName: feed.title,
      channelUrl: channelUrl,
      fetchedAt: new Date().toISOString()
    })).filter(video => video.id && video.title);
    
    console.log(`   ✅ Found ${videos.length} videos`);
    return videos;
    
  } catch (error) {
    console.error(`   ❌ Error fetching ${channelUrl}:`, error.message);
    return [];
  }
}

// ===== MAIN EXECUTION =====

async function main() {
  console.log('🚀 Starting YouTube RSS Fetcher...\n');
  
  if (CHANNELS.length === 0) {
    console.log('❌ No channels configured. Please add channel URLs to the CHANNELS array.');
    return;
  }
  
  // Load existing videos
  const existingVideos = loadExistingVideos();
  const existingIds = new Set(existingVideos.map(v => v.id));
  
  console.log(`📚 Loaded ${existingVideos.length} existing videos\n`);
  
  // Fetch new videos
  const allNewVideos = [];
  
  for (const channel of CHANNELS) {
    const videos = await fetchChannelVideos(channel);
    const newVideos = videos.filter(video => !existingIds.has(video.id));
    allNewVideos.push(...newVideos);
    
    if (newVideos.length > 0) {
      console.log(`   🆕 ${newVideos.length} new videos from this channel`);
    }
    
    // Small delay to be nice to YouTube
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Combine and save
  const allVideos = [...existingVideos, ...allNewVideos];
  
  // Sort by published date (newest first)
  allVideos.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt));
  
  console.log(`\n📊 Summary:`);
  console.log(`   Total videos: ${allVideos.length}`);
  console.log(`   New videos: ${allNewVideos.length}`);
  
  if (allNewVideos.length > 0) {
    console.log(`\n🆕 New videos:`);
    allNewVideos.forEach(video => {
      console.log(`   • ${video.title} (${video.channelName})`);
    });
  }
  
  saveVideos(allVideos);
  console.log('\n✨ Done!');
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main, fetchChannelVideos, loadExistingVideos };
