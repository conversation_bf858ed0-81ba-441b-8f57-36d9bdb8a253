#!/usr/bin/env node

/**
 * Simple Video Viewer
 * 
 * View and filter your fetched YouTube videos
 */

const fs = require('fs');
const { loadExistingVideos } = require('./simple-fetcher');

const OUTPUT_FILE = 'videos.json';

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  });
}

function formatDuration(seconds) {
  if (!seconds) return '';
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
}

function displayVideos(videos, limit = 10) {
  if (videos.length === 0) {
    console.log('📭 No videos found');
    return;
  }

  console.log(`📺 Showing ${Math.min(limit, videos.length)} of ${videos.length} videos:\n`);
  
  videos.slice(0, limit).forEach((video, index) => {
    console.log(`${(index + 1).toString().padStart(2, ' ')}. ${video.title}`);
    console.log(`    📺 ${video.channelName}`);
    console.log(`    📅 ${formatDate(video.publishedAt)}`);
    console.log(`    🔗 ${video.url}`);
    if (video.description && video.description.length > 100) {
      console.log(`    📝 ${video.description.substring(0, 100)}...`);
    }
    console.log('');
  });
}

function filterByChannel(videos, channelName) {
  return videos.filter(video => 
    video.channelName.toLowerCase().includes(channelName.toLowerCase())
  );
}

function filterByKeyword(videos, keyword) {
  return videos.filter(video => 
    video.title.toLowerCase().includes(keyword.toLowerCase()) ||
    video.description.toLowerCase().includes(keyword.toLowerCase())
  );
}

function filterByDays(videos, days) {
  const cutoff = new Date();
  cutoff.setDate(cutoff.getDate() - days);
  
  return videos.filter(video => 
    new Date(video.publishedAt) >= cutoff
  );
}

function showStats(videos) {
  const channels = {};
  videos.forEach(video => {
    channels[video.channelName] = (channels[video.channelName] || 0) + 1;
  });

  console.log('📊 Statistics:');
  console.log(`   Total videos: ${videos.length}`);
  console.log(`   Channels: ${Object.keys(channels).length}`);
  console.log('');
  
  console.log('📺 Videos per channel:');
  Object.entries(channels)
    .sort(([,a], [,b]) => b - a)
    .forEach(([channel, count]) => {
      console.log(`   ${channel}: ${count} videos`);
    });
  console.log('');
}

function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!fs.existsSync(OUTPUT_FILE)) {
    console.log(`❌ No videos found. Run 'node simple-fetcher.js' first.`);
    return;
  }

  const videos = loadExistingVideos();

  switch (command) {
    case 'stats':
      showStats(videos);
      break;
      
    case 'channel':
      if (!args[1]) {
        console.log('Usage: node viewer.js channel <channel_name>');
        return;
      }
      const channelVideos = filterByChannel(videos, args[1]);
      console.log(`🔍 Filtering by channel: "${args[1]}"\n`);
      displayVideos(channelVideos);
      break;
      
    case 'search':
      if (!args[1]) {
        console.log('Usage: node viewer.js search <keyword>');
        return;
      }
      const searchResults = filterByKeyword(videos, args[1]);
      console.log(`🔍 Searching for: "${args[1]}"\n`);
      displayVideos(searchResults);
      break;
      
    case 'recent':
      const days = parseInt(args[1]) || 7;
      const recentVideos = filterByDays(videos, days);
      console.log(`🔍 Videos from last ${days} days:\n`);
      displayVideos(recentVideos);
      break;
      
    case 'list':
    default:
      const limit = parseInt(args[1]) || 10;
      displayVideos(videos, limit);
      break;
  }
}

if (require.main === module) {
  main();
}

// Show help if no arguments
if (process.argv.length === 2) {
  console.log('📺 YouTube Video Viewer');
  console.log('');
  console.log('Usage:');
  console.log('  node viewer.js [command] [options]');
  console.log('');
  console.log('Commands:');
  console.log('  list [limit]           Show latest videos (default: 10)');
  console.log('  stats                  Show statistics');
  console.log('  channel <name>         Filter by channel name');
  console.log('  search <keyword>       Search in titles and descriptions');
  console.log('  recent [days]          Show videos from last N days (default: 7)');
  console.log('');
  console.log('Examples:');
  console.log('  node viewer.js list 20');
  console.log('  node viewer.js channel Fireship');
  console.log('  node viewer.js search "AI"');
  console.log('  node viewer.js recent 3');
}
