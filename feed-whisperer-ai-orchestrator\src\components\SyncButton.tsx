
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { RefreshCw } from 'lucide-react';

interface SyncButtonProps {
  channelId: string;
  onSyncComplete?: () => void;
  size?: 'sm' | 'default' | 'lg';
  variant?: 'default' | 'outline' | 'secondary';
}

export const SyncButton: React.FC<SyncButtonProps> = ({ 
  channelId, 
  onSyncComplete,
  size = 'sm',
  variant = 'outline'
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleSync = async () => {
    setIsLoading(true);

    try {
      const { data, error } = await supabase.functions.invoke('sync-youtube-videos', {
        body: { channelId }
      });

      if (error) throw error;

      toast({
        title: "Sync completed",
        description: data.message || "Videos have been synced successfully",
      });

      onSyncComplete?.();
    } catch (error) {
      console.error('Error syncing videos:', error);
      toast({
        title: "Sync failed",
        description: "Failed to sync videos. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button 
      onClick={handleSync} 
      disabled={isLoading}
      size={size}
      variant={variant}
      className="flex items-center space-x-1"
    >
      <RefreshCw className={`w-3 h-3 ${isLoading ? 'animate-spin' : ''}`} />
      <span>{isLoading ? 'Syncing...' : 'Sync'}</span>
    </Button>
  );
};
