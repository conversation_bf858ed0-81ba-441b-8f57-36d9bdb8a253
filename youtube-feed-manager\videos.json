[{"id": "cNywlNWan9o", "title": "5 easy (but critical) memory mistakes", "description": "Start building with <PERSON>el today: https://bit.ly/4mTm2D7\n\nThese 5 simple examples of memory management in C have the potential to cost your company billions of dollars or even put you in jail.\n\n#C #coding #programming \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\n- https://www.iso.org/standard/82075.html\n\n📚 Chapters\n- Bad bounds checking\n- Heartbleed bounds check\n- Using memory after it's been free()ed\n- Off-By-One\n- Double free() \n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n\n- Memory management in C\n- malloc() and free() in C\n- Bounds checking in C\n- The Morris Worm\n- What caused Heartbleed?", "url": "https://www.youtube.com/watch?v=cNywlNWan9o", "thumbnail": "https://i4.ytimg.com/vi/cNywlNWan9o/hqdefault.jpg", "publishedAt": "2025-06-09T15:44:07+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "Sd6F2pfKJmk", "title": "This Microsoft-backed AI startup just collapsed… why?", "description": "Try Brilliant free for 30 days https://brilliant.org/fireship You’ll also get 20% off an annual premium subscription.\n\nBuilder.ai, a UK-based startup backed by Microsoft and Softbank, has blown its valuation of $1.5 billion and collapsed back to zero. In today's video, we'll find out what went wrong and what it means for the future of ai coding startups.\n\n#ai #tech #vibecoding \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\nhttps://startups.co.uk/news/builder-ai-collapse/\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n- AI model updates\n- Why builder.ai failed \n- Is Ai a bubble?", "url": "https://www.youtube.com/watch?v=Sd6F2pfKJmk", "thumbnail": "https://i4.ytimg.com/vi/Sd6F2pfKJmk/hqdefault.jpg", "publishedAt": "2025-06-03T15:00:46+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "NLjnOsP_q1U", "title": "Google goes wild, again... 11 things you missed at I/O", "description": "Get up to 67% off VPS at Hostinger. Use code FIRESHIP for an extra discount at https://hostinger.com/fireship\n\nLet's take a look at what Google I/O 2025 means for developers, from some overpowered Gemini updates to the next new coding agents to make you obsolete.\n\n#ai #google #gemini \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\nhttps://io.google/2025/\n\n📚 Chapters\n- Google AI Ultra\n- Gemini 2.5 Benchmarks\n- How Gemini beat Pokémon Blue\n- Gemma 3n\n- Chrome AI\n- Agents everywhere\n- Android XR Glasses\n- Beam\n- Flow\n- CSS Carousel Primitives\n\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered", "url": "https://www.youtube.com/watch?v=NLjnOsP_q1U", "thumbnail": "https://i3.ytimg.com/vi/NLjnOsP_q1U/hqdefault.jpg", "publishedAt": "2025-05-21T17:27:32+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "NIgrGqmoeHs", "title": "Microsoft just opened the flood gates…", "description": "Get the free 80,000 hours career guide https://80000hours.org/fireship\n\nMicrosoft just made GitHub Copilot free and open source software under the MIT License. As of today, you can fork it, modify it and even build your own billion dollar competitor without going to prison.\n\n#ai #coding #microsoft \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\nMIT License: https://opensource.org/license/MIT\nGitHub Copilot: https://github.com/features/copilot\nWSL: https://learn.microsoft.com/en-us/windows/wsl/about\n\n📚 Chapters\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n- How Microsoft open sourced GitHub Copilot\n- Windows Subsystem for Linux (WSL)", "url": "https://www.youtube.com/watch?v=NIgrGqmoeHs", "thumbnail": "https://i3.ytimg.com/vi/NIgrGqmoeHs/hqdefault.jpg", "publishedAt": "2025-05-20T15:04:55+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "jCTvblRXnzg", "title": "Google’s AlphaEvolve is making new discoveries in math…", "description": "Learn cyber security for FREE with TryHackMe https://tryhackme.com/fireship You’ll also get 20% off an annual premium subscription\n\nLet's take a first look at AlphaEvolve - Google's AI system for creating algorithms that can make new discoveries in math and science. \n\n#ai #programming #thecodereport \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\n\nAlphaEvolve https://deepmind.google/discover/blog/alphaevolve-a-gemini-powered-coding-agent-for-designing-advanced-algorithms/\nGoogle Gemini https://youtu.be/59wV96Kc3dQ\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n\n- What is AlphaEvolve?\n- Can AI solve math problems\n- Latest breakthroughs in artificial intelligence", "url": "https://www.youtube.com/watch?v=jCTvblRXnzg", "thumbnail": "https://i3.ytimg.com/vi/jCTvblRXnzg/hqdefault.jpg", "publishedAt": "2025-05-16T14:36:26+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "6fnmXX8RK0s", "title": "5 weird (but useful) data structures in computer science", "description": "Try out the awesome new CodeRabbit VS code extension for free https://coderabbit.link/fireship-vscode\n\nLet's look at five weird data structures that have you covered when the arrays and hashmaps of this world can't get the job done.\n\n#programming #code #computerscience \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\nhttps://youtu.be/SmyPTnlqhlk\n\n📚 Chapters\n\n- B-tree\n- Radix tree\n- Rope\n- Bloom filter\n- Cuckoo hashing\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n\n- lesser known data structures for coding", "url": "https://www.youtube.com/watch?v=6fnmXX8RK0s", "thumbnail": "https://i3.ytimg.com/vi/6fnmXX8RK0s/hqdefault.jpg", "publishedAt": "2025-05-15T17:00:16+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "vVYlCnNjEWA", "title": "Multi-tenant SaaS apps with Next.js and Vercel", "description": "Learn how to build SaaS applications which can serve multiple users from a single codebase.\n\nhttps://vercel.com/templates/next.js/platforms-starter-kit\nhttps://vercel.com/guides/nextjs-multi-tenant-application\nhttps://nextjs.org/docs/app/guides/authentication", "url": "https://www.youtube.com/watch?v=vVYlCnNjEWA", "thumbnail": "https://i3.ytimg.com/vi/vVYlCnNjEWA/hqdefault.jpg", "publishedAt": "2025-05-13T21:00:56+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "59wV96Kc3dQ", "title": "Google must be cooking up something big...", "description": "Deploy your app without complexity using Sevalla https://sevalla.com/fireship\n\nLet's take a look at the new and improved Gemini 2.5 and its enhanced coding abilities. In addition, we'll analyze the recent move by OpenAI to remain controlled by a non-profit. \n\n#programming #ai #thecodereport\n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\n\nGemini 2.5 Improved https://developers.googleblog.com/en/gemini-2-5-pro-io-improved-coding-performance\nGemini 2.0 Release https://youtu.be/k9xbh9LUYn0\nOpenAI o4 https://youtu.be/O-Vu-DMIU40\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n\nWhat is special about Gemini 2.5? \nWhat to expect at Google I/O 2025\nBest AI models for programming\nHow does OpenAI's corporate structure work?", "url": "https://www.youtube.com/watch?v=59wV96Kc3dQ", "thumbnail": "https://i2.ytimg.com/vi/59wV96Kc3dQ/hqdefault.jpg", "publishedAt": "2025-05-07T16:16:07+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "lLJbHHeFSsE", "title": "The growing divide among React developers…", "description": "react.gg is an interactive, challenge-based learning experience that will get you shipping modern React apps like a pro. Get 30% off during their launch sale at https://react.gg\n\nThere's been a growing divide among developers in the React ecosystem, and it's time we talk about it.\n\n🎨 My Editor Settings\n  - Atom One Dark \n  - vscode-icons\n  - Fira Code Font\n\n🔖 Topics Covered\n  - React Server Components\n  - Next.js\n  - Vercel", "url": "https://www.youtube.com/watch?v=lLJbHHeFSsE", "thumbnail": "https://i1.ytimg.com/vi/lLJbHHeFSsE/hqdefault.jpg", "publishedAt": "2025-05-05T17:38:30+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "vbhNyRNNYjc", "title": "Vercel Ship 2025 teaser", "description": "Vercel Ship is our one-day event for developers and business leaders:\n\n- Hear the latest updates in AI, compute, and more\n- Get insights and lessons learned, directly from experts and leading companies\n- Bring your laptop for live, interactive sessions led by the creators of Next.js, v0, and Vercel\n- Engage with our sponsors and try the v0 booth to build an app live\n\nJoin us June 25 in NYC or online.\n\nhttps://vercel.com/ship", "url": "https://www.youtube.com/watch?v=vbhNyRNNYjc", "thumbnail": "https://i3.ytimg.com/vi/vbhNyRNNYjc/hqdefault.jpg", "publishedAt": "2025-05-01T17:12:33+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "5aN4Xg0VvCs", "title": "Redditors shocked to learn they’re arguing with AI bots", "description": "Sign up for CodeRabbit using code FIRESHIP to get get 1-month free https://coderabbit.ai/fireship\n\nRedditors are being manipulated by AI-powered bots in the r/changemyview subreddit, as revealed by controversial research at the University of Zurich. Using AI bots in the name of science is one thing, but what happens when bad actors get their hands on these highly charismatic LLMs?\n\n#ai #coding #reddit \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🔗 Resources\nhttps://www.youtube.com/watch?v=Tw18-4U7mts\n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n- How AI chatbots manipulated Reddit users\n- Prompt injection\n- Vishing\n- Fine-Tuned Infiltration", "url": "https://www.youtube.com/watch?v=5aN4Xg0VvCs", "thumbnail": "https://i2.ytimg.com/vi/5aN4Xg0VvCs/hqdefault.jpg", "publishedAt": "2025-04-30T15:01:00+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "niWpfRyvs2U", "title": "7 Programming Myths that waste your time", "description": "Try Brilliant free for 30 days https://brilliant.org/fireship You’ll also get 20% off an annual premium subscription.\n\nIn today's video we'll debunk 7 smart ideas that waste your time as a programmer. For each myth, we'll look at why it's a trap, how it lures you in and most importantly how to avoid mistakes I've made in the past.\n\n#tech #coding #programming \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n📚 Chapters\n- The problem with bleeding edge tech\n- How theory holds you back\n- Clean code\n- Test coverage tooling\n- Strive for performance\n- Optimize for web scale\n- Vibe coding\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n- Coding best practices\n- How to code in 2025", "url": "https://www.youtube.com/watch?v=niWpfRyvs2U", "thumbnail": "https://i3.ytimg.com/vi/niWpfRyvs2U/hqdefault.jpg", "publishedAt": "2025-04-29T15:02:16+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "KDRwgbwq0_c", "title": "Next.js Hackathon Awards & Recap", "description": "<PERSON>ne in live to see the 3 category winners and overall winner of the Next.js hackathon.\n\nhttps://next-hackathon-2025.vercel.app/", "url": "https://www.youtube.com/watch?v=KDRwgbwq0_c", "thumbnail": "https://i4.ytimg.com/vi/KDRwgbwq0_c/hqdefault.jpg", "publishedAt": "2025-04-22T19:32:34+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "O-Vu-DMIU40", "title": "OpenAI launches \"genius\" o4 model with a programming CLI tool...", "description": "Try the best video streaming API for developers https://mux.com/fireship\n\nLet's take a first look at OpenAI's new o4 model and the codex CLI programming tool. Let's compare it to other AI programming tools like GitHub Copilot, Claude Code, and Firebase Studio. \n\n#ai #programming #thecodereport \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\n\nOpenAI Codex https://github.com/openai/codex\nGPT-4o image generator https://youtu.be/Bt-7YiNBvLE\nClaude 3.7 Code https://youtu.be/x2WtHZciC74\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n\n- OpenAI o4-mini first look\n- OpenAI codex quickstart tutorial\n- OpenAI Codex vs Claude Code\n- What are the best AI coding tools?\n- How to get started vibe coding?", "url": "https://www.youtube.com/watch?v=O-Vu-DMIU40", "thumbnail": "https://i4.ytimg.com/vi/O-Vu-DMIU40/hqdefault.jpg", "publishedAt": "2025-04-17T18:04:46+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "XNratwOrSiY", "title": "4chan penetrated by a gang of soyjaks…", "description": "Timescale makes Postgres unbelievably fast https://rtabench.com/ \n\nToday we’re diving into the muck of the internet to find out how the 4chan imageboard got hacked and by who, as well as covering the major cybersecurity rollercoaster of the CVE programme’s defunding and refunding.\n  \nEverything else:\n #programming #hacking #thecodereport \n\n 🔗 Related Videos \n\nAndrew Tate Hacked https://youtu.be/xR5d4Ba4FZg\nNext.js Middleware Exploit https://youtu.be/AaCnBOqyvIM\n\n 🔥 Get More Content - Upgrade to PRO Upgrade at https://fireship.io/pro \n\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\nAtom One Dark\nvscode-icons\nFira Code Font\n\n🔖 Topics Covered\n\n4 chan hack explained.\nWhy was CVE defunded & refunded? \nWho hacked 4 Chan? Soyjack party\nHacking php with pdf’s using postscript and ghostscript\nA warning to update your code base. \n4chan janitors doxxed", "url": "https://www.youtube.com/watch?v=XNratwOrSiY", "thumbnail": "https://i1.ytimg.com/vi/XNratwOrSiY/hqdefault.jpg", "publishedAt": "2025-04-16T18:25:47+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "P4M9wfJH-yI", "title": "Meta’s Llama 4 is mindblowing… but did it cheat?", "description": "Write better code with Augment for free today https://fnf.dev/4jm7sS5\n\nLet's take a first look at Llama 4 herd - a new family of multi-modal large language models from Meta. We also breakdown a controversial memo from the CEO of Shopify about prioritizing AI throughout its business. \n\n#ai #programming #thecodereport \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\n\nLlama 4 https://ai.meta.com/blog/llama-4-multimodal-intelligence/\nShopify Memo https://x.com/tobi/status/1909231499448401946\nModel Context Protocol Explained https://youtu.be/HyzlYwjoXOQ\nGoogle Gemini https://youtu.be/HyzlYwjoXOQ\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n\n- Llama 4 herd explained\n- Best open-source AI models\n- Llama 4 vs Deepseek\n- Llama vs ChatGPT\n- Will humans be replaced by AI", "url": "https://www.youtube.com/watch?v=P4M9wfJH-yI", "thumbnail": "https://i1.ytimg.com/vi/P4M9wfJH-yI/hqdefault.jpg", "publishedAt": "2025-04-08T16:01:07+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "LjbNtw14TwI", "title": "Respected computer scientist mysteriously disappears...", "description": "Learn cyber security for FREE with TryHackMe https://tryhackme.com/fireship You’ll also get 20% off an annual premium subscription\n\nLet's take a look at the strange disappearance of cybersecurity expert and professor <PERSON><PERSON>. In addition, we examine recent hacking cases where the attacker sabotaged their company from within. \n\n#programming #cybersecurity #thecodereport \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Related Videos\n\nAndrew <PERSON> Hacked https://youtu.be/xR5d4Ba4FZg\nApple Backdoor https://youtu.be/ozkg_iW9mNU\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n\n- Who is <PERSON><PERSON>?\n- Why was <PERSON><PERSON> fired?\n- Cases of programming sabotage\n- Computer scientists who disappeared", "url": "https://www.youtube.com/watch?v=LjbNtw14TwI", "thumbnail": "https://i1.ytimg.com/vi/LjbNtw14TwI/hqdefault.jpg", "publishedAt": "2025-04-02T19:43:08+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "HyzlYwjoXOQ", "title": "I gave <PERSON> root access to my server... Model Context Protocol explained", "description": "Deploy your app without complexity and $50 in free credits on Sevalla https://sevalla.com/fireship\n\nLearn the fundamentals of Anthropic's Model Context Protocol by building an MCP server can give any AI model superpowers. In this tutorial, we build an TypeScript server that provides <PERSON> with additional context and the ability to modify data on the server. \n\n#programming #ai #thecodereport \n\n💬 Chat with Me on Discord\n\nhttps://discord.gg/fireship\n\n🔗 Resources\n\nModel Context Protocol https://modelcontextprotocol.io\nFuture Trends in Tech https://youtu.be/v4H2fTgHGuc\n\n🔥 Get More Content - Upgrade to PRO\n\nUpgrade at https://fireship.io/pro\nUse code YT25 for 25% off PRO access \n\n🎨 My Editor Settings\n\n- Atom One Dark \n- vscode-icons\n- Fira Code Font\n\n🔖 Topics Covered\n\n- Model Context Protocol explained\n- Basic MCP tutorial in TypeScript\n- How to build your own AI agent from scratch", "url": "https://www.youtube.com/watch?v=HyzlYwjoXOQ", "thumbnail": "https://i1.ytimg.com/vi/HyzlYwjoXOQ/hqdefault.jpg", "publishedAt": "2025-03-31T15:00:31+00:00", "channelName": "Fireship", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCsBjURrPoezykLs9EqgamOA", "fetchedAt": "2025-06-10T12:22:32.613Z"}, {"id": "EiBi-92DIps", "title": "Building AI platforms on Vercel", "description": "Use these three open-source templates to build AI platforms and applications on Vercel.\n\n- Claim deployments: https://claim-deployments-demo.vercel.app\n- AI chatbot: https://chat.vercel.ai\n- Slackbot: https://sdk.vercel.ai/docs/guides/slackbot", "url": "https://www.youtube.com/watch?v=EiBi-92DIps", "thumbnail": "https://i2.ytimg.com/vi/EiBi-92DIps/hqdefault.jpg", "publishedAt": "2025-03-06T22:50:02+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "G-ngjNfMnvE", "title": "How Fluid compute works", "description": "Try the demo here: https://fluid0.vercel.app\n\nAnnouncement post: https://vercel.com/blog/introducing-fluid-compute", "url": "https://www.youtube.com/watch?v=G-ngjNfMnvE", "thumbnail": "https://i4.ytimg.com/vi/G-ngjNfMnvE/hqdefault.jpg", "publishedAt": "2025-02-27T13:00:53+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "8a4_AAds0vU", "title": "Introducing Fluid compute: The power of servers, in serverless form", "description": "Fluid compute combines the efficiency of servers and the flexibility of serverless, enabling real-time, dynamic workloads like APIs, streaming, and AI.\n\nhttps://vercel.com/fluid\n\nBlog post → https://vercel.com/blog/introducing-fluid-compute\nChangelog → https://vercel.com/changelog/vercel-functions-can-now-run-on-fluid-compute\nDocumentation → https://vercel.com/docs/functions/fluid-compute", "url": "https://www.youtube.com/watch?v=8a4_AAds0vU", "thumbnail": "https://i1.ytimg.com/vi/8a4_AAds0vU/hqdefault.jpg", "publishedAt": "2025-02-06T23:06:18+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "itSu3T1zJew", "title": "Fluid compute with Vercel Functions", "description": "Fluid is our fast, cost-efficient compute. It's available today.\n\nhttps://vercel.com/blog/introducing-fluid-compute\nhttps://vercel.com/fluid", "url": "https://www.youtube.com/watch?v=itSu3T1zJew", "thumbnail": "https://i2.ytimg.com/vi/itSu3T1zJew/hqdefault.jpg", "publishedAt": "2025-02-04T22:13:46+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "cyFVtaLy-bA", "title": "Build a fullstack app in 7 minutes with v0 (Figma to code)", "description": "v0 is a web development assistant that helps you build faster with AI. You can import and generate working applications from your Figma designs, iterate and improve, and finally deploy your new application to Vercel in a few clicks.\n\nTry v0: https://v0.dev", "url": "https://www.youtube.com/watch?v=cyFVtaLy-bA", "thumbnail": "https://i4.ytimg.com/vi/cyFVtaLy-bA/hqdefault.jpg", "publishedAt": "2024-12-17T15:10:41+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "LxbktYCXKhg", "title": "Vercel has databases now", "description": "You can purchase and use first-party integrations with Supabase, Neon, Upstash, Redis Labs, Nile, Motherduck, EdgeDB, and more soon – with the Vercel Marketplace.\n\nhttps://vercel.com/marketplace", "url": "https://www.youtube.com/watch?v=LxbktYCXKhg", "thumbnail": "https://i1.ytimg.com/vi/LxbktYCXKhg/hqdefault.jpg", "publishedAt": "2024-12-16T13:42:52+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "KQvGYBVgsv4", "title": "A fireside chat with <PERSON> and <PERSON><PERSON>", "description": "The Vercel and Gumroad CEOs sit for a fireside chat to close out Next.js Conf 2024. They discuss the evolution of building web apps: what got us here, the technologies we're leaving behind, and the future of web development with AI.\n\nRead the full Next.js Conf 2024 recap: https://vercel.com/blog/recap-next-js-conf-2024\n\nDeploy your Next.js app with Vercel: https://vercel.com/new", "url": "https://www.youtube.com/watch?v=KQvGYBVgsv4", "thumbnail": "https://i4.ytimg.com/vi/KQvGYBVgsv4/hqdefault.jpg", "publishedAt": "2024-11-25T14:46:43+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "zL1f0SJmzt0", "title": "Optimizing LCP: Partial Prerendering deep dive (<PERSON>)", "description": "<PERSON> from the Next.js team explores how Partial Prerendering optimizes web performance by improving Core Web Vitals metrics: Time to First Byte (TTFB), First Contentful Paint (FCP), and Largest Contentful Paint (LCP).\n\n00:00 - Intro\n5:45 - Demo #1 \n9:05 - Migration\n21:58 - Demo #2 \n22:58 - Results\n\nRead the full Next.js Conf 2024 recap: https://vercel.com/blog/recap-next-js-conf-2024\n\nDeploy your Next.js app with Vercel: https://vercel.com/new", "url": "https://www.youtube.com/watch?v=zL1f0SJmzt0", "thumbnail": "https://i3.ytimg.com/vi/zL1f0SJmzt0/hqdefault.jpg", "publishedAt": "2024-11-25T14:46:38+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "pgE6oiOjHys", "title": "Live by default (Sanity)", "description": "Live by default\n\nSanity unveils their content concept Live by Default, which balances fresh content and top performance by turning every fetch into live-updating content—without trade-offs.\n\n00:00 - Intro\n2:32 - Request. Response. Repeat.\n5:50 - Solving the CDN Problem\n14:14 - Demo\n\nTo learn more about building “Live by default” Next.js applications visit https://sanity.io/live\n\nRead the full Next.js Conf 2024 recap: https://vercel.com/blog/recap-next-js-conf-2024\n\nDeploy your Next.js app with Vercel: https://vercel.com/new", "url": "https://www.youtube.com/watch?v=pgE6oiOjHys", "thumbnail": "https://i1.ytimg.com/vi/pgE6oiOjHys/hqdefault.jpg", "publishedAt": "2024-11-18T16:02:50+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "Pc2-6RXBRYc", "title": "Next level product velocity with Next.js (PayPal)", "description": "Next level product velocity with Next.js\n\nPayPal’s <PERSON> explains how they’re using App Router, tRPC, shadcn/ui, and Turborepo to replace their checkout's fragmented React and Express stack. From this, they’ve seen big improvements in product velocity and user experience.\n\n0:00 - Intro\n4:26 - Migration\n8:36 - Results\n17:26 - QA\n\nRead the full Next.js Conf 2024 recap: https://vercel.com/blog/recap-next-js-conf-2024\n\nDeploy your Next.js app with Vercel: https://vercel.com/new", "url": "https://www.youtube.com/watch?v=Pc2-6RXBRYc", "thumbnail": "https://i1.ytimg.com/vi/Pc2-6RXBRYc/hqdefault.jpg", "publishedAt": "2024-11-18T16:02:40+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "dqVL7mdTrlA", "title": "Building user interfaces in the age of AI (Perplexity)", "description": "Building user interfaces in the age of AI\n\nPerplexity’s <PERSON><PERSON> talks about building AI UIs, including:\n\n- Streaming\n- Schema validation\n- Rendering structured output from LLMs\n- React Server Components\n\n00:00: Intro\n5:36: Migration\n12:10: Results\n13:10 - <PERSON><PERSON>\n\nRead the full Next.js Conf 2024 recap: https://vercel.com/blog/recap-next-js-conf-2024\n\nDeploy your Next.js app with Vercel - https://vercel.com/new", "url": "https://www.youtube.com/watch?v=dqVL7mdTrlA", "thumbnail": "https://i1.ytimg.com/vi/dqVL7mdTrlA/hqdefault.jpg", "publishedAt": "2024-11-12T15:12:01+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}, {"id": "Z77fshyTF3w", "title": "Incrementally migrating to Next.js from a client-side-only app", "description": "The long and winding road: CSR to static export to SSG\n\nNick <PERSON> recaps Peloton’s multi-year migration to Next.js from a client-side-only app in a large monorepo, leading to improved page performance, productivity, and streamlined architecture. \n\n00:00 - Intro\n2:21 - Migration\n10:23 - Results\n\nRead the full Next.js Conf 2024 recap: https://vercel.com/blog/recap-next-js-conf-2024\n\nDeploy your Next.js app with Vercel: https://vercel.com/new", "url": "https://www.youtube.com/watch?v=Z77fshyTF3w", "thumbnail": "https://i3.ytimg.com/vi/Z77fshyTF3w/hqdefault.jpg", "publishedAt": "2024-11-12T15:11:49+00:00", "channelName": "Vercel", "channelUrl": "https://www.youtube.com/feeds/videos.xml?channel_id=UCLq8gNoee7oXM7MvTdjyQvA", "fetchedAt": "2025-06-10T12:22:33.658Z"}]