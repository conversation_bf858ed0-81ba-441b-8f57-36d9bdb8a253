// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://yvtbgofmxruoazbsaeix.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inl2dGJnb2ZteHJ1b2F6YnNhZWl4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1NTY4MTcsImV4cCI6MjA2NTEzMjgxN30.4MblKRDVbkTYUbeW3JIl6j8ihFzvYltGSaB87_XotkQ";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);