
import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export const FilterPanel = () => {
  // Mock filter rules
  const filterRules = [
    {
      id: '1',
      name: 'High Priority AI Content',
      description: 'AI, ML, LLM keywords with high engagement',
      isActive: true,
      sourceTypes: ['youtube', 'rss'],
      actions: ['priority', 'notify']
    },
    {
      id: '2',
      name: 'Developer Tools',
      description: 'Content about development tools and frameworks',
      isActive: true,
      sourceTypes: ['youtube', 'instagram'],
      actions: ['tag', 'priority']
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold">Filter Rules & Automation</h2>
        <Button>Create New Rule</Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Active Rules</h3>
          {filterRules.map((rule) => (
            <Card key={rule.id}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-base">{rule.name}</CardTitle>
                  <Badge variant={rule.isActive ? 'default' : 'secondary'}>
                    {rule.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  {rule.description}
                </p>
                
                <div className="flex flex-wrap gap-1">
                  {rule.sourceTypes.map((type) => (
                    <Badge key={type} variant="outline" className="text-xs">
                      {type}
                    </Badge>
                  ))}
                </div>

                <div className="flex flex-wrap gap-1">
                  {rule.actions.map((action) => (
                    <Badge key={action} className="text-xs bg-blue-100 text-blue-800">
                      {action}
                    </Badge>
                  ))}
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">Edit</Button>
                  <Button variant="outline" size="sm">Test</Button>
                  <Button variant="outline" size="sm">Duplicate</Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Rule Templates</h3>
          <Card className="border-dashed">
            <CardContent className="p-6 text-center space-y-3">
              <div className="text-2xl">🎯</div>
              <h4 className="font-semibold">Smart Content Filtering</h4>
              <p className="text-sm text-muted-foreground">
                Create rules to automatically categorize, prioritize, and route content based on keywords, source, engagement metrics, and more.
              </p>
              <Button className="w-full">Create Your First Rule</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                📊 Analytics Dashboard
              </Button>
              <Button variant="outline" className="w-full justify-start">
                🔄 Sync All Sources
              </Button>
              <Button variant="outline" className="w-full justify-start">
                ⚙️ Global Settings
              </Button>
              <Button variant="outline" className="w-full justify-start">
                📤 Export Rules
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
